version: 1

inbounds:
  1:
    type: 'siemens'
    id: '************'
    ip: "************"
    port: 102
    model: "828D"
    param_config:
      path:
        - axes:
            - name: "X"
              num: 1
            - name: "Y"
              num: 2
            - name: "Z"
              num: 3
            - name: "A"
              num: 5
            - name: "C"
              num: 6
          spindles:
            - name: "S1"
              num: 4

rules:
  - name: syntec_status
    description: "syntec_status"
    rules:
    - source:
        source: "2 if var1 else 0"
        placeholder:
          var1: "$.data"
      source_name: "current"
      target: "$.State"
      type: "expr"
    - source:
        source: "2 if var1 != 1024 else 0"
        placeholder:
          var1: "$.data.result"
      source_name: "current"
      target: "$.State"
      type: "expr"
    - source:
        source: "$.data.path[0].status"
        mapping:
          ACTIVE: 1
        default: 2
      source_name: "current"
      target: "$.State"
      type: "map"
    - source: "$.id"
      target: "$.id"
      type: "reference"
    - source:
        source: "TIMESTAMP_TO_STR(var1)"
        placeholder:
          var1: "$.timestamp"
      target: "$.CreateDate"
      type: "expr"
    - source:
        source: "UUID1()"
      target: "$.Uuid"
      type: "expr"
    trigger:
      mode: "change"
      type: "any"
      exclude: ["$.CreateDate", "$.Uuid"]

  - name: syntec_count
    description: "syntec_count"
    rules:
    - source: "$.data.part_count"
      source_name: "current"
      target: "$.Count"
      type: "reference"
    - source: "$.id"
      target: "$.id"
      type: "reference"
    - source:
        source: "TIMESTAMP_TO_STR(var1)"
        placeholder:
          var1: "$.timestamp"
      target: "$.CreateDate"
      type: "expr"
    - source:
        source: "UUID1()"
      target: "$.Uuid"
      type: "expr"
    trigger:
      mode: "change"
      type: "any"
      exclude: ["$.CreateDate", "$.Uuid"]

  - name: syntec_alarm
    description: "syntec_alarm"
    rules:
    - source:
        source: "1 if (var1 and len(var1)) or var2 == 'TRIGGERED' else 0"
        placeholder:
          var1: "$.data.alarm"
          var2: "$.data.emg"
      source_name: "current"
      target: "$.State"
      type: "expr"
    - source: "$.id"
      target: "$.id"
      type: "reference"
    - source:
        source: "TIMESTAMP_TO_STR(var1)"
        placeholder:
          var1: "$.timestamp"
      target: "$.CreateDate"
      type: "expr"
    - source:
        source: "UUID1()"
      target: "$.Uuid"
      type: "expr"
    trigger:
      mode: "change"
      type: "any"
      exclude: ["$.CreateDate", "$.Uuid"]

  - name: syntec_current
    description: "current"
    rules:
    - source: "$.data.path[0].main_prog"
      source_name: "current"
      target: "$.main_prog"
      type: "reference"
    - source: "$.data.path[0].cur_prog"
      source_name: "current"
      target: "$.cur_prog"
      type: "reference"

    - source: "$.data.path[0].cur_seq"
      source_name: "current"
      target: "$.cur_seq"
      type: "reference"
    - source: "$.data.path[0].ov_feed"
      source_name: "current"
      target: "$.ov_feed"
      type: "reference"
    - source: "$.data.path[0].act_feed"
      source_name: "current"
      target: "$.act_feed"
      type: "reference"
    - source: "$.data.path[0].spindles"
      source_name: "current"
      target: "$.spindles"
      type: "reference"
    - source: "$.data.path[0].axes"
      source_name: "current"
      target: "$.axes"
      type: "reference"

    - source: "$.data.emg"
      source_name: "current"
      target: "$.emg"
      type: "reference"
    - source: "$.data.mode"
      source_name: "current"
      target: "$.mode"
      type: "reference"
    - source: "$.data.alarm"
      source_name: "current"
      target: "$.alarm"
      type: "reference"
    - source: "$.id"
      target: "$.id"
      type: "reference"
    - source:
        source: "TIMESTAMP_TO_STR(var1)"
        placeholder:
          var1: "$.timestamp"
      target: "$.CreateDate"
      type: "expr"
    - source:
        source: "UUID1()"
      target: "$.Uuid"
      type: "expr"
    trigger:
      mode: "change"
      type: "any"
      exclude: ["$.CreateDate", "$.Uuid"]


outbounds:
  custom_ioteq:
    config:
      api_host: http://*************:9890/eq/eqEquipmentAccTabs/
      odbc_url: mssql+pyodbc://sa:123456@*************:1433/test?driver=ODBC+Driver+17+for+SQL+Server
    type: custom_ioteq
    
flows:
# inbound只有一种输出, outbound只有一种输入
  - inbounds: [1] # == ['gt01']
    name: flow01
    rules:
      - name: "syntec_status"
        action: "IOTEqStatues"
      - name: "syntec_alarm"
        action: "IOTEqWarning"
      - name: "syntec_count"
        action: "IOTEqProduceCount"
      - name: "syntec_current"
        action: "IOTEqMachiningParams"
    outbound: 'custom_ioteq'

apps: []
# inbound有多种输出，可能需要多个输出结合到一个输入
  # - inbounds: [2,3]
  #   outbound: 'xinheyun'
  #   rules:
  #     - name: "rule01"
  #       onputs: ["sysinfo", "current"]
  #       input: "status"
        
